<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐍 精美贪吃蛇游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .game-container {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .game-header {
            text-align: center;
            margin-bottom: 20px;
            color: white;
        }

        .game-title {
            font-size: 2.5em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: titleGlow 2s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            from { filter: drop-shadow(0 0 5px #FFD700); }
            to { filter: drop-shadow(0 0 15px #FFA500); }
        }

        .score-board {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .score-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 15px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        #gameCanvas {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: linear-gradient(45deg, #2d5a27, #4a7c59);
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
            display: block;
            margin: 0 auto;
        }

        .controls {
            margin-top: 20px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            border: none;
            color: white;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }

        .btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.6);
        }

        .game-over-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border-radius: 20px;
            color: white;
            text-align: center;
        }

        .game-over-title {
            font-size: 3em;
            margin-bottom: 20px;
            color: #FF6B6B;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            animation: gameOverPulse 1s ease-in-out infinite alternate;
        }

        @keyframes gameOverPulse {
            from { transform: scale(1); }
            to { transform: scale(1.05); }
        }

        .final-score {
            font-size: 1.5em;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .difficulty-selector {
            margin: 15px 0;
        }

        .difficulty-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin: 0 5px;
            padding: 8px 16px;
            font-size: 0.9em;
        }

        .difficulty-btn.active {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #333;
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
            border-radius: 20px;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }

        .instructions {
            margin-top: 15px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
            text-align: center;
        }

        .sound-toggle {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            transition: all 0.3s ease;
        }

        .sound-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .achievement-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #333;
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.5);
            z-index: 1000;
            animation: achievementSlide 0.5s ease-out;
            max-width: 300px;
        }

        @keyframes achievementSlide {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .achievement-content {
            display: flex;
            align-items: center;
        }

        .achievement-icon {
            font-size: 2em;
            margin-right: 10px;
        }

        .achievement-title {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .achievement-desc {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .pause-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border-radius: 20px;
            color: white;
            text-align: center;
        }

        .pause-title {
            font-size: 2.5em;
            margin-bottom: 20px;
            color: #4ECDC4;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        @media (max-width: 768px) {
            .game-container {
                margin: 10px;
                padding: 15px;
            }
            
            .game-title {
                font-size: 2em;
            }
            
            #gameCanvas {
                width: 300px;
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <button class="sound-toggle" id="soundToggle">🔊</button>
        <div class="particles" id="particles"></div>
        
        <div class="game-header">
            <h1 class="game-title">🐍 贪吃蛇大冒险</h1>
            
            <div class="score-board">
                <div class="score-item">
                    🏆 分数: <span id="score">0</span>
                </div>
                <div class="score-item">
                    📏 长度: <span id="length">1</span>
                </div>
                <div class="score-item">
                    ⚡ 最高: <span id="highScore">0</span>
                </div>
            </div>
            
            <div class="difficulty-selector">
                <button class="btn difficulty-btn active" data-difficulty="easy">🐌 简单</button>
                <button class="btn difficulty-btn" data-difficulty="normal">🐍 普通</button>
                <button class="btn difficulty-btn" data-difficulty="hard">🚀 困难</button>
            </div>
        </div>

        <canvas id="gameCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <button class="btn" id="startBtn">🎮 开始游戏</button>
            <button class="btn secondary" id="pauseBtn">⏸️ 暂停</button>
            <button class="btn secondary" id="resetBtn">🔄 重新开始</button>
        </div>

        <div class="instructions">
            🎯 使用方向键、WASD 或滑动手势控制蛇的移动<br>
            🍎 吃掉食物获得分数，避免撞到墙壁和自己<br>
            🏆 达到不同分数解锁新成就！
        </div>

        <div class="pause-screen" id="pauseScreen">
            <div class="pause-title">⏸️ 游戏暂停</div>
            <p>按空格键或点击继续按钮恢复游戏</p>
            <button class="btn secondary" onclick="game.togglePause()">▶️ 继续游戏</button>
        </div>

        <div class="game-over-screen" id="gameOverScreen">
            <div class="game-over-title">🎮 游戏结束</div>
            <div class="final-score">最终分数: <span id="finalScore">0</span></div>
            <button class="btn" id="restartBtn">🔄 再来一局</button>
        </div>
    </div>

    <div class="achievement-notification" id="achievementNotification" style="display: none;">
        <div class="achievement-content">
            <div class="achievement-icon">🏆</div>
            <div class="achievement-text">
                <div class="achievement-title">成就解锁！</div>
                <div class="achievement-desc" id="achievementDesc"></div>
            </div>
        </div>
    </div>

    <script>
        class SnakeGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.gridSize = 20;
                this.tileCount = this.canvas.width / this.gridSize;

                // 游戏状态
                this.snake = [{x: 10, y: 10}];
                this.food = this.generateFood();
                this.dx = 0;
                this.dy = 0;
                this.score = 0;
                this.gameRunning = false;
                this.gamePaused = false;
                this.difficulty = 'normal';
                this.gameSpeed = 150;

                // 动画和效果
                this.particles = [];
                this.foodAnimation = 0;
                this.snakeAnimation = 0;

                // 音效系统
                this.soundEnabled = true;
                this.sounds = this.createSounds();

                // 成就系统
                this.achievements = this.loadAchievements();
                this.unlockedAchievements = JSON.parse(localStorage.getItem('snakeAchievements') || '[]');

                this.initializeGame();
                this.createParticles();
                this.loadHighScore();
            }

            initializeGame() {
                this.bindEvents();
                this.updateDisplay();
                this.gameLoop();
            }

            bindEvents() {
                // 键盘控制
                document.addEventListener('keydown', (e) => {
                    if (!this.gameRunning || this.gamePaused) return;

                    switch(e.key) {
                        case 'ArrowUp':
                        case 'w':
                        case 'W':
                            if (this.dy !== 1) { this.dx = 0; this.dy = -1; }
                            break;
                        case 'ArrowDown':
                        case 's':
                        case 'S':
                            if (this.dy !== -1) { this.dx = 0; this.dy = 1; }
                            break;
                        case 'ArrowLeft':
                        case 'a':
                        case 'A':
                            if (this.dx !== 1) { this.dx = -1; this.dy = 0; }
                            break;
                        case 'ArrowRight':
                        case 'd':
                        case 'D':
                            if (this.dx !== -1) { this.dx = 1; this.dy = 0; }
                            break;
                        case ' ':
                            this.togglePause();
                            break;
                    }
                    e.preventDefault();
                });

                // 按钮事件
                document.getElementById('startBtn').addEventListener('click', () => this.startGame());
                document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                document.getElementById('restartBtn').addEventListener('click', () => this.restartGame());

                // 难度选择
                document.querySelectorAll('.difficulty-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.setDifficulty(e.target.dataset.difficulty);
                    });
                });

                // 触摸控制
                this.addTouchControls();

                // 音效按钮
                document.getElementById('soundToggle').addEventListener('click', () => this.toggleSound());
            }

            createSounds() {
                // 使用Web Audio API创建简单音效
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                return {
                    eat: () => this.playTone(audioContext, 800, 0.1),
                    gameOver: () => this.playTone(audioContext, 200, 0.5),
                    move: () => this.playTone(audioContext, 400, 0.05)
                };
            }

            playTone(audioContext, frequency, duration) {
                if (!this.soundEnabled) return;

                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = frequency;
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            }

            toggleSound() {
                this.soundEnabled = !this.soundEnabled;
                document.getElementById('soundToggle').textContent = this.soundEnabled ? '🔊' : '🔇';
            }

            loadAchievements() {
                return [
                    { id: 'first_food', name: '初次品尝', desc: '吃到第一个食物', condition: () => this.score >= 10 },
                    { id: 'growing_up', name: '茁壮成长', desc: '蛇身长度达到5节', condition: () => this.snake.length >= 5 },
                    { id: 'century_club', name: '百分俱乐部', desc: '获得100分', condition: () => this.score >= 100 },
                    { id: 'speed_demon', name: '速度恶魔', desc: '在困难模式下获得50分', condition: () => this.score >= 50 && this.difficulty === 'hard' },
                    { id: 'long_snake', name: '巨蟒', desc: '蛇身长度达到10节', condition: () => this.snake.length >= 10 },
                    { id: 'high_scorer', name: '高分达人', desc: '获得200分', condition: () => this.score >= 200 },
                    { id: 'master_snake', name: '蛇王', desc: '获得500分', condition: () => this.score >= 500 }
                ];
            }

            checkAchievements() {
                this.achievements.forEach(achievement => {
                    if (!this.unlockedAchievements.includes(achievement.id) && achievement.condition()) {
                        this.unlockAchievement(achievement);
                    }
                });
            }

            unlockAchievement(achievement) {
                this.unlockedAchievements.push(achievement.id);
                localStorage.setItem('snakeAchievements', JSON.stringify(this.unlockedAchievements));
                this.showAchievementNotification(achievement);
            }

            showAchievementNotification(achievement) {
                const notification = document.getElementById('achievementNotification');
                const desc = document.getElementById('achievementDesc');

                desc.textContent = achievement.name + ' - ' + achievement.desc;
                notification.style.display = 'block';

                setTimeout(() => {
                    notification.style.display = 'none';
                }, 3000);
            }

            addTouchControls() {
                let touchStartX = 0;
                let touchStartY = 0;

                this.canvas.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    touchStartX = e.touches[0].clientX;
                    touchStartY = e.touches[0].clientY;
                });

                this.canvas.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    if (!this.gameRunning || this.gamePaused) return;

                    const touchEndX = e.changedTouches[0].clientX;
                    const touchEndY = e.changedTouches[0].clientY;

                    const deltaX = touchEndX - touchStartX;
                    const deltaY = touchEndY - touchStartY;

                    const minSwipeDistance = 30;

                    if (Math.abs(deltaX) > Math.abs(deltaY)) {
                        // 水平滑动
                        if (Math.abs(deltaX) > minSwipeDistance) {
                            if (deltaX > 0 && this.dx !== -1) {
                                this.dx = 1; this.dy = 0; // 右
                            } else if (deltaX < 0 && this.dx !== 1) {
                                this.dx = -1; this.dy = 0; // 左
                            }
                        }
                    } else {
                        // 垂直滑动
                        if (Math.abs(deltaY) > minSwipeDistance) {
                            if (deltaY > 0 && this.dy !== -1) {
                                this.dx = 0; this.dy = 1; // 下
                            } else if (deltaY < 0 && this.dy !== 1) {
                                this.dx = 0; this.dy = -1; // 上
                            }
                        }
                    }
                });
            }

            setDifficulty(difficulty) {
                this.difficulty = difficulty;
                switch(difficulty) {
                    case 'easy':
                        this.gameSpeed = 200;
                        break;
                    case 'normal':
                        this.gameSpeed = 150;
                        break;
                    case 'hard':
                        this.gameSpeed = 100;
                        break;
                }
            }

            generateFood() {
                let food;
                do {
                    food = {
                        x: Math.floor(Math.random() * this.tileCount),
                        y: Math.floor(Math.random() * this.tileCount)
                    };
                } while (this.snake.some(segment => segment.x === food.x && segment.y === food.y));
                return food;
            }

            startGame() {
                if (!this.gameRunning) {
                    this.gameRunning = true;
                    this.gamePaused = false;
                    document.getElementById('startBtn').textContent = '🎮 游戏中...';
                    document.getElementById('startBtn').disabled = true;
                }
            }

            togglePause() {
                if (this.gameRunning) {
                    this.gamePaused = !this.gamePaused;
                    document.getElementById('pauseBtn').textContent = this.gamePaused ? '▶️ 继续' : '⏸️ 暂停';
                    document.getElementById('pauseScreen').style.display = this.gamePaused ? 'flex' : 'none';
                }
            }

            resetGame() {
                this.snake = [{x: 10, y: 10}];
                this.food = this.generateFood();
                this.dx = 0;
                this.dy = 0;
                this.score = 0;
                this.gameRunning = false;
                this.gamePaused = false;
                this.particles = [];

                document.getElementById('startBtn').textContent = '🎮 开始游戏';
                document.getElementById('startBtn').disabled = false;
                document.getElementById('pauseBtn').textContent = '⏸️ 暂停';
                document.getElementById('gameOverScreen').style.display = 'none';
                document.getElementById('pauseScreen').style.display = 'none';

                this.updateDisplay();
                this.createParticles();
            }

            restartGame() {
                this.resetGame();
                this.startGame();
            }

            gameLoop() {
                setTimeout(() => {
                    if (this.gameRunning && !this.gamePaused) {
                        this.update();
                    }
                    this.draw();
                    this.gameLoop();
                }, this.gameSpeed);
            }

            update() {
                // 移动蛇头
                const head = {x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy};

                // 检查碰撞
                if (this.checkCollision(head)) {
                    this.gameOver();
                    return;
                }

                this.snake.unshift(head);

                // 检查是否吃到食物
                if (head.x === this.food.x && head.y === this.food.y) {
                    this.score += this.difficulty === 'easy' ? 10 : this.difficulty === 'normal' ? 15 : 20;
                    this.food = this.generateFood();
                    this.createFoodParticles();
                    this.sounds.eat();
                    this.updateDisplay();
                    this.checkAchievements();
                } else {
                    this.snake.pop();
                }

                this.snakeAnimation += 0.1;
                this.foodAnimation += 0.2;
            }

            checkCollision(head) {
                // 检查墙壁碰撞
                if (head.x < 0 || head.x >= this.tileCount || head.y < 0 || head.y >= this.tileCount) {
                    return true;
                }

                // 检查自身碰撞
                return this.snake.some(segment => segment.x === head.x && segment.y === head.y);
            }

            gameOver() {
                this.gameRunning = false;
                this.sounds.gameOver();
                this.saveHighScore();
                document.getElementById('finalScore').textContent = this.score;
                document.getElementById('gameOverScreen').style.display = 'flex';
                document.getElementById('startBtn').textContent = '🎮 开始游戏';
                document.getElementById('startBtn').disabled = false;
            }

            draw() {
                // 清空画布
                this.ctx.fillStyle = 'rgba(45, 90, 39, 0.1)';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // 绘制网格背景
                this.drawGrid();

                // 绘制食物
                this.drawFood();

                // 绘制蛇
                this.drawSnake();

                // 绘制粒子效果
                this.drawParticles();
            }

            drawGrid() {
                this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
                this.ctx.lineWidth = 1;

                for (let i = 0; i <= this.tileCount; i++) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(i * this.gridSize, 0);
                    this.ctx.lineTo(i * this.gridSize, this.canvas.height);
                    this.ctx.stroke();

                    this.ctx.beginPath();
                    this.ctx.moveTo(0, i * this.gridSize);
                    this.ctx.lineTo(this.canvas.width, i * this.gridSize);
                    this.ctx.stroke();
                }
            }

            drawSnake() {
                this.snake.forEach((segment, index) => {
                    const x = segment.x * this.gridSize;
                    const y = segment.y * this.gridSize;

                    if (index === 0) {
                        // 绘制蛇头
                        this.drawSnakeHead(x, y);
                    } else {
                        // 绘制蛇身
                        this.drawSnakeBody(x, y, index);
                    }
                });
            }

            drawSnakeHead(x, y) {
                const centerX = x + this.gridSize / 2;
                const centerY = y + this.gridSize / 2;
                const radius = this.gridSize / 2 - 2;

                // 蛇头主体
                const gradient = this.ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
                gradient.addColorStop(0, '#4CAF50');
                gradient.addColorStop(1, '#2E7D32');

                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                this.ctx.fill();

                // 蛇眼睛 - 根据移动方向调整位置
                let eyeOffsetX = 0, eyeOffsetY = -4;
                if (this.dx === 1) { eyeOffsetX = 2; eyeOffsetY = 0; } // 右
                else if (this.dx === -1) { eyeOffsetX = -2; eyeOffsetY = 0; } // 左
                else if (this.dy === 1) { eyeOffsetX = 0; eyeOffsetY = 2; } // 下
                else if (this.dy === -1) { eyeOffsetX = 0; eyeOffsetY = -4; } // 上

                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.beginPath();
                this.ctx.arc(centerX - 4 + eyeOffsetX, centerY + eyeOffsetY, 3, 0, 2 * Math.PI);
                this.ctx.arc(centerX + 4 + eyeOffsetX, centerY + eyeOffsetY, 3, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.fillStyle = '#000000';
                this.ctx.beginPath();
                this.ctx.arc(centerX - 4 + eyeOffsetX, centerY + eyeOffsetY, 1.5, 0, 2 * Math.PI);
                this.ctx.arc(centerX + 4 + eyeOffsetX, centerY + eyeOffsetY, 1.5, 0, 2 * Math.PI);
                this.ctx.fill();

                // 蛇舌头
                if (Math.sin(this.snakeAnimation) > 0) {
                    this.ctx.strokeStyle = '#FF5722';
                    this.ctx.lineWidth = 2;
                    this.ctx.beginPath();
                    this.ctx.moveTo(centerX, centerY + 2);
                    this.ctx.lineTo(centerX, centerY + 8);
                    this.ctx.stroke();
                }
            }

            drawSnakeBody(x, y, index) {
                const centerX = x + this.gridSize / 2;
                const centerY = y + this.gridSize / 2;
                const radius = this.gridSize / 2 - 3;

                // 身体渐变色
                const intensity = 1 - (index / this.snake.length) * 0.5;
                const gradient = this.ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
                gradient.addColorStop(0, `rgba(76, 175, 80, ${intensity})`);
                gradient.addColorStop(1, `rgba(46, 125, 50, ${intensity})`);

                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                this.ctx.fill();

                // 身体纹理
                this.ctx.strokeStyle = `rgba(255, 255, 255, ${0.3 * intensity})`;
                this.ctx.lineWidth = 1;
                this.ctx.stroke();
            }

            drawFood() {
                const x = this.food.x * this.gridSize;
                const y = this.food.y * this.gridSize;
                const centerX = x + this.gridSize / 2;
                const centerY = y + this.gridSize / 2;
                const radius = this.gridSize / 2 - 2 + Math.sin(this.foodAnimation) * 2;

                // 食物主体（苹果）
                const gradient = this.ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
                gradient.addColorStop(0, '#FF5722');
                gradient.addColorStop(0.7, '#D32F2F');
                gradient.addColorStop(1, '#B71C1C');

                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                this.ctx.fill();

                // 苹果叶子
                this.ctx.fillStyle = '#4CAF50';
                this.ctx.beginPath();
                this.ctx.ellipse(centerX + 2, centerY - radius + 2, 3, 6, Math.PI / 4, 0, 2 * Math.PI);
                this.ctx.fill();

                // 发光效果
                this.ctx.shadowColor = '#FF5722';
                this.ctx.shadowBlur = 10;
                this.ctx.beginPath();
                this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                this.ctx.stroke();
                this.ctx.shadowBlur = 0;
            }

            createParticles() {
                // 创建背景粒子
                for (let i = 0; i < 20; i++) {
                    this.particles.push({
                        x: Math.random() * this.canvas.width,
                        y: Math.random() * this.canvas.height,
                        vx: (Math.random() - 0.5) * 2,
                        vy: (Math.random() - 0.5) * 2,
                        life: Math.random() * 100 + 50,
                        maxLife: 150,
                        type: 'background'
                    });
                }
            }

            createFoodParticles() {
                // 创建食物被吃掉的粒子效果
                const x = this.food.x * this.gridSize + this.gridSize / 2;
                const y = this.food.y * this.gridSize + this.gridSize / 2;

                for (let i = 0; i < 10; i++) {
                    this.particles.push({
                        x: x,
                        y: y,
                        vx: (Math.random() - 0.5) * 8,
                        vy: (Math.random() - 0.5) * 8,
                        life: 30,
                        maxLife: 30,
                        type: 'food',
                        color: '#FF5722'
                    });
                }
            }

            drawParticles() {
                this.particles = this.particles.filter(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.life--;

                    if (particle.type === 'background') {
                        // 背景粒子循环
                        if (particle.x < 0) particle.x = this.canvas.width;
                        if (particle.x > this.canvas.width) particle.x = 0;
                        if (particle.y < 0) particle.y = this.canvas.height;
                        if (particle.y > this.canvas.height) particle.y = 0;

                        const alpha = Math.sin(particle.life * 0.1) * 0.5 + 0.5;
                        this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.3})`;
                        this.ctx.beginPath();
                        this.ctx.arc(particle.x, particle.y, 2, 0, 2 * Math.PI);
                        this.ctx.fill();

                        return true;
                    } else if (particle.type === 'food') {
                        // 食物粒子
                        const alpha = particle.life / particle.maxLife;
                        this.ctx.fillStyle = `rgba(255, 87, 34, ${alpha})`;
                        this.ctx.beginPath();
                        this.ctx.arc(particle.x, particle.y, 3 * alpha, 0, 2 * Math.PI);
                        this.ctx.fill();

                        return particle.life > 0;
                    }

                    return particle.life > 0;
                });
            }

            updateDisplay() {
                document.getElementById('score').textContent = this.score;
                document.getElementById('length').textContent = this.snake.length;
            }

            loadHighScore() {
                const highScore = localStorage.getItem('snakeHighScore') || 0;
                document.getElementById('highScore').textContent = highScore;
            }

            saveHighScore() {
                const currentHigh = parseInt(localStorage.getItem('snakeHighScore') || 0);
                if (this.score > currentHigh) {
                    localStorage.setItem('snakeHighScore', this.score);
                    document.getElementById('highScore').textContent = this.score;
                }
            }
        }

        // 创建页面粒子效果
        function createPageParticles() {
            const particlesContainer = document.getElementById('particles');

            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 3 + 's';
                particle.style.animationDuration = (Math.random() * 2 + 2) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 游戏实例化和启动
        let game;
        window.addEventListener('load', () => {
            game = new SnakeGame();
            createPageParticles();
        });
    </script>
</body>
</html>
