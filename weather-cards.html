<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精品天气动画卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: white;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 40px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 14px;
            font-weight: 500;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .weather-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            max-width: 1200px;
            width: 100%;
        }

        .weather-card {
            position: relative;
            height: 350px;
            border-radius: 20px;
            overflow: hidden;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .weather-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-content {
            position: relative;
            z-index: 10;
            padding: 30px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .weather-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .weather-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .weather-desc {
            font-size: 1rem;
            opacity: 0.8;
            line-height: 1.5;
        }

        .temperature {
            font-size: 3rem;
            font-weight: 200;
            margin-top: auto;
        }

        /* 晴天样式 */
        .sunny {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .sunny::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: sunRotate 20s linear infinite;
        }

        /* 雨天样式 */
        .rainy {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        /* 雪天样式 */
        .snowy {
            background: linear-gradient(135deg, #ddd6fe 0%, #a78bfa 100%);
            color: #374151;
        }

        /* 风天样式 */
        .windy {
            background: linear-gradient(135deg, #81ecec 0%, #00b894 100%);
            color: #2d3436;
        }

        /* 动画效果 */
        @keyframes sunRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes sway {
            0%, 100% { transform: rotate(-5deg); }
            50% { transform: rotate(5deg); }
        }

        @keyframes fall {
            to {
                transform: translateY(100vh);
                opacity: 0;
            }
        }

        @keyframes drift {
            0% { transform: translateX(-10px); }
            50% { transform: translateX(10px); }
            100% { transform: translateX(-10px); }
        }

        /* 雨滴动画 */
        .rain-drop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.8));
            border-radius: 0 0 2px 2px;
            animation: fall linear infinite;
        }

        /* 雪花动画 */
        .snowflake {
            position: absolute;
            color: white;
            font-size: 1rem;
            animation: fall linear infinite, drift ease-in-out infinite;
        }

        /* 风线动画 */
        .wind-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.6), transparent);
            border-radius: 1px;
            animation: drift 3s ease-in-out infinite;
        }

        /* 云朵动画 */
        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            animation: float 6s ease-in-out infinite;
        }

        .cloud::before,
        .cloud::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50px;
        }

        .cloud::before {
            width: var(--before-width, 30px);
            height: var(--before-height, 20px);
            top: -10px;
            left: 10px;
        }

        .cloud::after {
            width: var(--after-width, 25px);
            height: var(--after-height, 15px);
            top: -5px;
            right: 10px;
        }

        /* 水洼效果 */
        .puddle {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            animation: puddleGrow 4s ease-in-out infinite;
        }

        @keyframes puddleGrow {
            0%, 100% { width: 60px; opacity: 0.2; }
            50% { width: 100px; opacity: 0.4; }
        }

        /* 光线效果 */
        .sun-ray {
            position: absolute;
            top: 20%;
            left: 20%;
            width: 2px;
            height: 60px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
            transform-origin: bottom;
            animation: rayRotate 8s linear infinite;
        }

        @keyframes rayRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 树木摇摆效果 */
        .tree {
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 2rem;
            animation: sway 3s ease-in-out infinite;
        }

        /* 增强的悬停效果 */
        .weather-card:hover .weather-icon {
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }

        /* 渐变文字效果 */
        .gradient-text {
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 粒子效果 */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particleFloat 6s ease-in-out infinite;
        }

        @keyframes particleFloat {
            0%, 100% {
                transform: translateY(0) translateX(0) scale(1);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            50% {
                transform: translateY(-100px) translateX(20px) scale(1.2);
            }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .weather-container {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                gap: 10px;
            }
            
            .control-btn {
                padding: 10px 20px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="gradient-text">精品天气动画</h1>
        <p>体验苹果级别的天气可视化效果</p>
    </div>

    <div class="controls">
        <button class="control-btn active" onclick="showWeather('all')">全部显示</button>
        <button class="control-btn" onclick="showWeather('sunny')">☀️ 晴天</button>
        <button class="control-btn" onclick="showWeather('rainy')">🌧️ 雨天</button>
        <button class="control-btn" onclick="showWeather('snowy')">❄️ 雪天</button>
        <button class="control-btn" onclick="showWeather('windy')">💨 风天</button>
    </div>

    <div class="weather-container">
        <!-- 晴天卡片 -->
        <div class="weather-card sunny" data-weather="sunny">
            <div class="sun-ray"></div>
            <div class="sun-ray" style="animation-delay: 1s; transform: rotate(45deg);"></div>
            <div class="sun-ray" style="animation-delay: 2s; transform: rotate(90deg);"></div>
            <div class="sun-ray" style="animation-delay: 3s; transform: rotate(135deg);"></div>
            <div class="card-content">
                <div>
                    <div class="weather-icon">☀️</div>
                    <div class="weather-title">晴朗</div>
                    <div class="weather-desc">阳光明媚，万里无云<br>适合户外活动</div>
                </div>
                <div class="temperature">28°</div>
            </div>
        </div>

        <!-- 雨天卡片 -->
        <div class="weather-card rainy" data-weather="rainy">
            <div class="puddle"></div>
            <div class="card-content">
                <div>
                    <div class="weather-icon">🌧️</div>
                    <div class="weather-title">雨天</div>
                    <div class="weather-desc">细雨绵绵，空气清新<br>记得带伞出门</div>
                </div>
                <div class="temperature">18°</div>
            </div>
        </div>

        <!-- 雪天卡片 -->
        <div class="weather-card snowy" data-weather="snowy">
            <div class="card-content">
                <div>
                    <div class="weather-icon">❄️</div>
                    <div class="weather-title">雪天</div>
                    <div class="weather-desc">雪花飞舞，银装素裹<br>注意保暖防滑</div>
                </div>
                <div class="temperature">-2°</div>
            </div>
        </div>

        <!-- 风天卡片 -->
        <div class="weather-card windy" data-weather="windy">
            <div class="tree">🌳</div>
            <div class="card-content">
                <div>
                    <div class="weather-icon">💨</div>
                    <div class="weather-title">大风</div>
                    <div class="weather-desc">风力强劲，云朵飘移<br>外出注意安全</div>
                </div>
                <div class="temperature">15°</div>
            </div>
        </div>
    </div>

    <script>
        // 天气动画控制
        function createRainDrops() {
            const rainyCards = document.querySelectorAll('.rainy');
            rainyCards.forEach(card => {
                // 清除现有雨滴
                card.querySelectorAll('.rain-drop').forEach(drop => drop.remove());
                
                for (let i = 0; i < 50; i++) {
                    const drop = document.createElement('div');
                    drop.className = 'rain-drop';
                    drop.style.left = Math.random() * 100 + '%';
                    drop.style.animationDuration = (Math.random() * 1 + 0.5) + 's';
                    drop.style.animationDelay = Math.random() * 2 + 's';
                    card.appendChild(drop);
                }
            });
        }

        function createSnowflakes() {
            const snowyCards = document.querySelectorAll('.snowy');
            snowyCards.forEach(card => {
                // 清除现有雪花
                card.querySelectorAll('.snowflake').forEach(flake => flake.remove());
                
                for (let i = 0; i < 30; i++) {
                    const flake = document.createElement('div');
                    flake.className = 'snowflake';
                    flake.innerHTML = '❄';
                    flake.style.left = Math.random() * 100 + '%';
                    flake.style.animationDuration = (Math.random() * 3 + 2) + 's';
                    flake.style.animationDelay = Math.random() * 2 + 's';
                    flake.style.fontSize = (Math.random() * 0.8 + 0.8) + 'rem';
                    card.appendChild(flake);
                }
            });
        }

        function createWindLines() {
            const windyCards = document.querySelectorAll('.windy');
            windyCards.forEach(card => {
                // 清除现有风线
                card.querySelectorAll('.wind-line').forEach(line => line.remove());
                
                for (let i = 0; i < 8; i++) {
                    const line = document.createElement('div');
                    line.className = 'wind-line';
                    line.style.top = Math.random() * 80 + 10 + '%';
                    line.style.left = '0%';
                    line.style.width = Math.random() * 60 + 40 + '%';
                    line.style.animationDelay = Math.random() * 2 + 's';
                    line.style.animationDuration = (Math.random() * 2 + 2) + 's';
                    card.appendChild(line);
                }
            });
        }

        function createClouds() {
            const windyCards = document.querySelectorAll('.windy');
            windyCards.forEach(card => {
                // 清除现有云朵
                card.querySelectorAll('.cloud').forEach(cloud => cloud.remove());

                for (let i = 0; i < 3; i++) {
                    const cloud = document.createElement('div');
                    cloud.className = 'cloud';
                    cloud.style.width = Math.random() * 60 + 40 + 'px';
                    cloud.style.height = Math.random() * 30 + 20 + 'px';
                    cloud.style.top = Math.random() * 50 + 10 + '%';
                    cloud.style.left = Math.random() * 70 + '%';
                    cloud.style.animationDelay = Math.random() * 3 + 's';

                    // 添加云朵的附加部分
                    cloud.style.setProperty('--before-width', Math.random() * 40 + 30 + 'px');
                    cloud.style.setProperty('--before-height', Math.random() * 25 + 15 + 'px');
                    cloud.style.setProperty('--after-width', Math.random() * 35 + 25 + 'px');
                    cloud.style.setProperty('--after-height', Math.random() * 20 + 15 + 'px');

                    card.appendChild(cloud);
                }
            });
        }

        function createParticles() {
            const sunnyCards = document.querySelectorAll('.sunny');
            sunnyCards.forEach(card => {
                // 清除现有粒子
                card.querySelectorAll('.particle').forEach(particle => particle.remove());

                for (let i = 0; i < 15; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.bottom = '0%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                    card.appendChild(particle);
                }
            });
        }

        function createLightning() {
            const rainyCards = document.querySelectorAll('.rainy');
            rainyCards.forEach(card => {
                if (Math.random() < 0.3) { // 30% 概率出现闪电
                    const lightning = document.createElement('div');
                    lightning.style.position = 'absolute';
                    lightning.style.top = '0';
                    lightning.style.left = '0';
                    lightning.style.width = '100%';
                    lightning.style.height = '100%';
                    lightning.style.background = 'rgba(255, 255, 255, 0.9)';
                    lightning.style.zIndex = '5';
                    lightning.style.opacity = '0';
                    lightning.style.transition = 'opacity 0.1s';

                    card.appendChild(lightning);

                    setTimeout(() => {
                        lightning.style.opacity = '1';
                        setTimeout(() => {
                            lightning.style.opacity = '0';
                            setTimeout(() => {
                                lightning.remove();
                            }, 100);
                        }, 100);
                    }, Math.random() * 3000);
                }
            });
        }

        // 天气切换功能
        function showWeather(type) {
            const cards = document.querySelectorAll('.weather-card');
            const buttons = document.querySelectorAll('.control-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (type === 'all') {
                cards.forEach(card => {
                    card.classList.remove('hidden');
                });
            } else {
                cards.forEach(card => {
                    if (card.dataset.weather === type) {
                        card.classList.remove('hidden');
                    } else {
                        card.classList.add('hidden');
                    }
                });
            }
            
            // 重新创建动画效果
            setTimeout(() => {
                createRainDrops();
                createSnowflakes();
                createWindLines();
                createClouds();
                createParticles();
                createLightning();
            }, 100);
        }

        // 初始化动画
        document.addEventListener('DOMContentLoaded', function() {
            createRainDrops();
            createSnowflakes();
            createWindLines();
            createClouds();
            createParticles();

            // 定期刷新动画
            setInterval(() => {
                createRainDrops();
                createSnowflakes();
                createWindLines();
                createClouds();
                createParticles();
            }, 10000);

            // 定期创建闪电效果
            setInterval(() => {
                createLightning();
            }, 8000);
        });

        // 添加卡片点击效果
        document.querySelectorAll('.weather-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
